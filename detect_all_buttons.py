import cv2
import numpy as np
import sys
import os

# Add the current directory to the Python path to import from buttonControl
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from buttonControl.button_detection import detect_buttons_by_status

def detect_all_buttons_in_image(image_path, verbose=True, display_process=True):
    """
    Detect all buttons and knob in an image during 'uncertain' state.
    
    Args:
        image_path (str): Path to the input image
        verbose (bool): Whether to print detailed information
        display_process (bool): Whether to show processing steps
    
    Returns:
        tuple: Detection results from detect_buttons function
    """
    # Read the image
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not read image: {image_path}")
    
    # Detect all buttons in uncertain state
    detection_result = detect_buttons_by_status(
        image,
        status='uncertain',  # Changed from 'visible' to 'uncertain' for better compatibility
        verbose=verbose,
        display_process=display_process
    )

    return detection_result
    

if __name__ == "__main__":
    # 检测所有按钮
    from pathlib import Path
    import cv2

    # 准备图片列表
    mustPassList = list(Path('./data/button-debug-saves/allButtons/mustPass').glob('*.jpg')) + list(Path('./data/button-debug-saves/allButtons/mustPass').glob('*.png'))
    mustPassList = [img for img in mustPassList]
    toPassList = list(Path('./data/button-debug-saves/allButtons/toPass').glob('*.jpg')) + list(Path('./data/button-debug-saves/allButtons/toPass').glob('*.png'))
    toPassList = [img for img in toPassList]
    mustPass_success_count = 0
    toPass_success_count = 0

    # 准备输出目录
    mustPassDetectionDir = Path('./data/button-debug-saves/allButtons/mustPassDetection')
    toPassDetectionDir = Path('./data/button-debug-saves/allButtons/toPassDetection')
    mustPassDetectionDir.mkdir(parents=True, exist_ok=True)
    toPassDetectionDir.mkdir(parents=True, exist_ok=True)

    for imgList, outDir in zip([mustPassList, toPassList], [mustPassDetectionDir, toPassDetectionDir]):
        total = len(imgList)
        for img in imgList:
            detection_result = detect_all_buttons_in_image(
                str(img),
                verbose=True,
                display_process=True
            )

            # 保存检测结果图像（如果有）
            display_img = None
            if isinstance(detection_result, dict):
                display_img = detection_result.get('display_img')
                target = detection_result.get('target')
                # 判断成功
                if target:
                    if imgList == mustPassList:
                        mustPass_success_count += 1
                    else:
                        toPass_success_count += 1
            elif isinstance(detection_result, tuple):
                # 约定tuple第一个为display_img
                display_img = detection_result[0]
                top_row = detection_result[1]
                bottom_row = detection_result[2]
                # 判断成功标准：top_row和bottom_row都非空且有按钮
                success = False
                if (top_row and any(top_row)) and (bottom_row and any(bottom_row)):
                    success = True
                if success:
                    if imgList == mustPassList:
                        mustPass_success_count += 1
                    else:
                        toPass_success_count += 1
            # # 保存图片
            # if display_img is not None:
            #     out_path = outDir / img.name
            #     cv2.imwrite(str(out_path), display_img)

    # 汇报成功率
    print("\n==============================")
    print(f"mustPass Success Rate: {mustPass_success_count}/{len(mustPassList)} ({(mustPass_success_count/len(mustPassList))*100:.1f}%)")
    print(f"toPass Success Rate: {toPass_success_count}/{len(toPassList)} ({(toPass_success_count/len(toPassList))*100:.1f}%)")
    print("==============================")
