import cv2
import numpy as np
from typing import Tuple, List, Optional

def detect_buttons_fixed(image, verbose=False, display_process=True):
    """
    Fixed version of button detection that handles glowing buttons better
    
    Args:
        image: Input image
        verbose: Whether to print debug information
        display_process: Whether to display intermediate results
    
    Returns:
        tuple: (display_img, top_row, bottom_row, knob, handle_angle, mode_code)
    """
    try:
        # Preprocess the image
        processed_img = preprocess_image_fixed(image)
        
        # Convert to different color spaces
        hsv = cv2.cvtColor(processed_img, cv2.COLOR_BGR2HSV)
        gray = cv2.cvtColor(processed_img, cv2.COLOR_BGR2GRAY)
        
        # Enhanced color detection for glowing buttons
        red_buttons, green_buttons = detect_colored_buttons_enhanced(processed_img, hsv, gray, verbose)
        
        # Detect bright buttons (for glowing state)
        bright_buttons = detect_bright_buttons_enhanced(hsv, gray, verbose)
        
        # Apply glow constraint and classify buttons
        classified_buttons = classify_buttons_by_position(red_buttons, green_buttons, bright_buttons, image.shape, verbose)
        
        # Extract top and bottom rows
        top_row = [classified_buttons.get('top_left'), classified_buttons.get('top_right')]
        bottom_row = [classified_buttons.get('bottom_left'), classified_buttons.get('bottom_right')]
        
        # Simple knob detection (placeholder)
        knob = None
        handle_angle = None
        mode_code = None
        
        if verbose:
            print(f"Classified buttons: {classified_buttons}")
            print(f"Top row: {top_row}")
            print(f"Bottom row: {bottom_row}")
        
        return processed_img, top_row, bottom_row, knob, handle_angle, mode_code
        
    except Exception as e:
        if verbose:
            print(f"Error in detect_buttons_fixed: {e}")
        return image, [None, None], [None, None], None, None, None

def preprocess_image_fixed(image, gamma=1.2):
    """Enhanced preprocessing for glowing button detection"""
    # Convert to float for processing
    img_float = image.astype(np.float32) / 255.0
    
    # White balance using gray world assumption
    img_mean_r = np.mean(img_float[:,:,0])
    img_mean_g = np.mean(img_float[:,:,1])
    img_mean_b = np.mean(img_float[:,:,2])
    
    # Avoid division by zero
    if img_mean_r > 0:
        img_float[:,:,0] = img_float[:,:,0] * (img_mean_g / img_mean_r)
    if img_mean_b > 0:
        img_float[:,:,2] = img_float[:,:,2] * (img_mean_g / img_mean_b)
    
    # Clip values to valid range
    img_float = np.clip(img_float, 0, 1)
    
    # Convert back to uint8
    balanced_image = (img_float * 255).astype(np.uint8)
    
    # Enhance contrast
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    lab = cv2.cvtColor(balanced_image, cv2.COLOR_BGR2LAB)
    lab_planes = list(cv2.split(lab))
    lab_planes[0] = clahe.apply(lab_planes[0])
    lab = cv2.merge(lab_planes)
    enhanced_image = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
    
    return enhanced_image

def detect_colored_buttons_enhanced(image, hsv, gray, verbose=False):
    """Enhanced colored button detection with better ranges for glowing buttons"""
    
    # Enhanced red detection (including glowing red -> yellow/white)
    red_lower1 = np.array([0, 30, 30])  # Lower saturation for glowing
    red_upper1 = np.array([15, 255, 255])  # Extended hue range
    red_lower2 = np.array([165, 30, 30])  # Lower saturation for glowing
    red_upper2 = np.array([180, 255, 255])
    
    red_mask1 = cv2.inRange(hsv, red_lower1, red_upper1)
    red_mask2 = cv2.inRange(hsv, red_lower2, red_upper2)
    red_mask = cv2.bitwise_or(red_mask1, red_mask2)
    
    # Enhanced green detection (including glowing green -> white)
    green_lower = np.array([35, 30, 30])  # Lower saturation for glowing
    green_upper = np.array([85, 255, 255])
    green_mask = cv2.inRange(hsv, green_lower, green_upper)
    
    # Morphological operations to clean up masks
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel)
    green_mask = cv2.morphologyEx(green_mask, cv2.MORPH_CLOSE, kernel)
    
    # Find contours and extract circular buttons
    red_buttons = extract_circular_buttons(red_mask, gray, min_radius=5, max_radius=50, verbose=verbose)
    green_buttons = extract_circular_buttons(green_mask, gray, min_radius=5, max_radius=50, verbose=verbose)
    
    if verbose:
        print(f"Enhanced detection - Red buttons: {len(red_buttons)}, Green buttons: {len(green_buttons)}")
    
    return red_buttons, green_buttons

def detect_bright_buttons_enhanced(hsv, gray, verbose=False):
    """Enhanced bright button detection for glowing states"""
    
    # Detect very bright regions (potential glowing buttons)
    _, bright_mask = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
    
    # Also detect high value in HSV (bright regardless of color)
    _, hsv_bright_mask = cv2.threshold(hsv[:,:,2], 220, 255, cv2.THRESH_BINARY)
    
    # Combine masks
    combined_bright = cv2.bitwise_or(bright_mask, hsv_bright_mask)
    
    # Morphological operations
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    combined_bright = cv2.morphologyEx(combined_bright, cv2.MORPH_CLOSE, kernel)
    
    # Extract circular bright regions
    bright_buttons = extract_circular_buttons(combined_bright, gray, min_radius=8, max_radius=50, verbose=verbose)
    
    if verbose:
        print(f"Bright buttons detected: {len(bright_buttons)}")
    
    return bright_buttons

def extract_circular_buttons(mask, gray_image, min_radius=5, max_radius=50, verbose=False):
    """Extract circular buttons from a binary mask"""
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    buttons = []
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < 50:  # Too small
            continue
            
        # Fit circle to contour
        (x, y), radius = cv2.minEnclosingCircle(contour)
        
        if min_radius <= radius <= max_radius:
            # Check circularity
            perimeter = cv2.arcLength(contour, True)
            if perimeter > 0:
                circularity = 4 * np.pi * area / (perimeter * perimeter)
                if circularity > 0.3:  # Reasonably circular
                    buttons.append((int(x), int(y), int(radius)))
    
    if verbose:
        print(f"Extracted {len(buttons)} circular buttons from mask")
    
    return buttons

def classify_buttons_by_position(red_buttons, green_buttons, bright_buttons, image_shape, verbose=False):
    """Classify buttons by their position in the image"""
    height, width = image_shape[:2]
    
    # Combine all detected buttons
    all_buttons = []
    
    # Add red buttons with type
    for button in red_buttons:
        all_buttons.append((*button, 'red'))
    
    # Add green buttons with type
    for button in green_buttons:
        all_buttons.append((*button, 'green'))
    
    # Add bright buttons (potential glowing buttons)
    for button in bright_buttons:
        all_buttons.append((*button, 'bright'))
    
    if len(all_buttons) < 4:
        if verbose:
            print(f"Not enough buttons detected: {len(all_buttons)}")
        return {}
    
    # Sort buttons by position
    # Top half vs bottom half
    top_buttons = [b for b in all_buttons if b[1] < height * 0.6]
    bottom_buttons = [b for b in all_buttons if b[1] >= height * 0.4]
    
    classified = {}
    
    # Classify top buttons (left vs right)
    if len(top_buttons) >= 2:
        top_buttons.sort(key=lambda x: x[0])  # Sort by x coordinate
        
        # Top left should be red (or bright if glowing)
        top_left_candidates = [b for b in top_buttons if b[3] in ['red', 'bright']]
        if top_left_candidates:
            classified['top_left'] = top_left_candidates[0][:3]
        
        # Top right should be green (or bright if glowing)
        top_right_candidates = [b for b in top_buttons if b[3] in ['green', 'bright']]
        if top_right_candidates:
            # Get the rightmost one
            top_right_candidates.sort(key=lambda x: x[0], reverse=True)
            classified['top_right'] = top_right_candidates[0][:3]
    
    # Classify bottom buttons
    if len(bottom_buttons) >= 2:
        bottom_buttons.sort(key=lambda x: x[0])  # Sort by x coordinate
        
        # Bottom left should be green
        bottom_left_candidates = [b for b in bottom_buttons if b[3] == 'green']
        if bottom_left_candidates:
            classified['bottom_left'] = bottom_left_candidates[0][:3]
        
        # Bottom right should be red
        bottom_right_candidates = [b for b in bottom_buttons if b[3] == 'red']
        if bottom_right_candidates:
            # Get the rightmost one
            bottom_right_candidates.sort(key=lambda x: x[0], reverse=True)
            classified['bottom_right'] = bottom_right_candidates[0][:3]
    
    if verbose:
        print(f"Button classification: {classified}")
    
    return classified

# Test the fixed detection
if __name__ == "__main__":
    import sys
    from pathlib import Path
    
    test_img_path = Path('./data/button-debug-saves/allButtons/mustPass/darkFull.jpg')
    if test_img_path.exists():
        image = cv2.imread(str(test_img_path))
        result = detect_buttons_fixed(image, verbose=True)
        print(f"Fixed detection result: {result[1:3]}")  # top_row, bottom_row