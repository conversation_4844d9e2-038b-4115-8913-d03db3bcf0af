import cv2
import numpy as np
import sys
import os
from pathlib import Path

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_detection():
    """Test the main detect_buttons function"""
    
    # Test one image from mustPass
    test_img_path = Path('./data/button-debug-saves/allButtons/mustPass/darkFull.jpg')
    
    if not test_img_path.exists():
        print(f"Test image not found: {test_img_path}")
        return
    
    print(f"Testing image: {test_img_path}")
    
    try:
        # Read image
        image = cv2.imread(str(test_img_path))
        if image is None:
            print("Failed to read image")
            return
        
        print(f"Image shape: {image.shape}")
        
        # Test the main detect_buttons function
        from buttonControl.button_detection import detect_buttons
        print("Testing detect_buttons...")
        
        result = detect_buttons(image, verbose=True, display_process=False)
        print(f"Main detection result: {result}")
        
        if isinstance(result, tuple) and len(result) >= 3:
            display_img, top_row, bottom_row = result[0], result[1], result[2]
            print(f"Top row: {top_row}")
            print(f"Bottom row: {bottom_row}")
            
            # Check if we have valid detections
            top_valid = top_row and len([x for x in top_row if x is not None]) >= 2
            bottom_valid = bottom_row and len([x for x in bottom_row if x is not None]) >= 2
            
            print(f"Top row valid: {top_valid}")
            print(f"Bottom row valid: {bottom_valid}")
            print(f"Overall success: {top_valid and bottom_valid}")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_main_detection()