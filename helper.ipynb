{"cells": [{"cell_type": "code", "execution_count": null, "id": "9c26b3bf", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "# 指定目录\n", "dir_path = './data/shot-images/'\n", "\n", "# 获取所有npy文件和jpg文件\n", "all_files = os.listdir(dir_path)\n", "npy_files = [f for f in all_files if f.endswith('.npy')]\n", "jpg_files = [f for f in all_files if f.endswith('.jpg')]\n", "\n", "# 获取所有jpg前缀集合\n", "jpg_prefixes = set(os.path.splitext(f)[0] for f in jpg_files)\n", "\n", "# 遍历npy文件，删除没有对应jpg前缀的npy\n", "for npy_file in npy_files:\n", "    prefix = os.path.splitext(npy_file)[0].replace('imd','imc')\n", "    if prefix not in jpg_prefixes:\n", "        npy_path = os.path.join(dir_path, npy_file)\n", "        print(f\"Deleting {npy_path} (no corresponding jpg)\")\n", "        os.remove(npy_path)\n"]}, {"cell_type": "code", "execution_count": null, "id": "a9fc4451", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 加载npy文件\n", "npy_path = './data/shot-images/imd_0801061250.npy'\n", "depth_data = np.load(npy_path)\n", "\n", "# 绘制深度分布图\n", "plt.figure(figsize=(8, 6))\n", "plt.imshow(depth_data, cmap='viridis')\n", "plt.colorbar(label='Depth Value')\n", "plt.title('Depth Distribution of imd_0801061250.npy')\n", "plt.xlabel('Width')\n", "plt.ylabel('Height')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "4cd0f22b", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import xml.etree.ElementTree as ET\n", "import cv2\n", "\n", "# 1. 获取所有xml文件\n", "xml_dir = Path('./data/shot-images/aa')\n", "xml_files = sorted(xml_dir.glob('*.xml'))\n", "\n", "print(f\"Total XML files found: {len(xml_files)}\")\n", "\n", "# 2. 解析每个xml，读取对应jpg，画出标注框\n", "for xml_file in xml_files:\n", "    try:\n", "        tree = ET.parse(xml_file)\n", "        root = tree.getroot()\n", "    \n", "        jpg_path = Path(str(xml_file).replace('.xml', '.jpg'))\n", "\n", "        if not jpg_path.exists():\n", "            print(f\"Image not found: {jpg_path}\")\n", "            continue\n", "\n", "        # 读取图片\n", "        img = cv2.imread(str(jpg_path))\n", "        if img is None:\n", "            print(f\"Failed to read image: {jpg_path}\")\n", "            continue\n", "\n", "        # 解析所有object\n", "        for obj in root.findall('object'):\n", "            name_elem = obj.find('name')\n", "            label = name_elem.text.strip() if name_elem is not None and name_elem.text else \"(none)\"\n", "            bndbox = obj.find('bndbox')\n", "            if bndbox is not None:\n", "                try:\n", "                    xmin = float(bndbox.find('xmin').text)\n", "                    ymin = float(bndbox.find('ymin').text)\n", "                    xmax = float(bndbox.find('xmax').text)\n", "                    ymax = float(bndbox.find('ymax').text)\n", "                    pt1 = (int(xmin), int(ymin))\n", "                    pt2 = (int(xmax), int(ymax))\n", "                    color = (0, 255, 0) if \"green\" in label else (0, 0, 255) if \"red\" in label else (255, 0, 0)\n", "                    cv2.rectangle(img, pt1, pt2, color, 2)\n", "                    cv2.putText(img, label, (pt1[0], pt1[1]-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1, cv2.LINE_AA)\n", "                except Exception as e:\n", "                    print(f\"Error parsing bndbox in {xml_file.name}: {e}\")\n", "\n", "        # 转为RGB用于matplotlib显示\n", "        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "        plt.figure(figsize=(8, 6))\n", "        plt.imshow(img_rgb)\n", "        plt.title(f\"{jpg_path.stem}\")\n", "        plt.axis('off')\n", "        plt.show()\n", "\n", "        # 只演示前3张\n", "        if xml_files.index(xml_file) >= 2:\n", "            break\n", "\n", "    except Exception as e:\n", "        print(f\"Error parsing {xml_file.name}: {e}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "53aea3c0", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import cv2\n", "from pathlib import Path\n", "import xml.etree.ElementTree as ET\n", "\n", "def compute_iou(box1, box2):\n", "    \"\"\"计算两个框的IoU\"\"\"\n", "    # box: (xmin, ymin, xmax, ymax)\n", "    xA = max(box1[0], box2[0])\n", "    yA = max(box1[1], box2[1])\n", "    xB = min(box1[2], box2[2])\n", "    yB = min(box1[3], box2[3])\n", "    interW = max(0, xB - xA)\n", "    interH = max(0, yB - yA)\n", "    interArea = interW * interH\n", "    area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])\n", "    area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])\n", "    unionArea = area1 + area2 - interArea\n", "    if unionArea == 0:\n", "        return 0\n", "    return interArea / unionArea\n", "\n", "def compute_area(box):\n", "    \"\"\"计算框的面积\"\"\"\n", "    return (box[2] - box[0]) * (box[3] - box[1])\n", "\n", "def nms_keep_smallest_improved(boxes, labels, iou_threshold=0.2):\n", "    \"\"\"\n", "    改进的NMS算法，在重叠情况下保留最小的框\n", "    \n", "    Args:\n", "        boxes: 框列表，格式为 [(xmin, ymin, xmax, ymax), ...]\n", "        labels: 标签列表\n", "        iou_threshold: IoU阈值（降低到0.2）\n", "    \n", "    Returns:\n", "        filtered_boxes: 过滤后的框\n", "        filtered_labels: 过滤后的标签\n", "    \"\"\"\n", "    if len(boxes) == 0:\n", "        return [], []\n", "    \n", "    # 转换为numpy数组以便处理\n", "    boxes = np.array(boxes)\n", "    labels = np.array(labels)\n", "    \n", "    # 计算所有框的面积\n", "    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])\n", "    \n", "    # 按面积从小到大排序（保留最小的框）\n", "    sorted_indices = np.argsort(areas)\n", "    \n", "    keep = []\n", "    suppressed = set()\n", "    \n", "    for i in sorted_indices:\n", "        if i in suppressed:\n", "            continue\n", "            \n", "        keep.append(i)\n", "        current_box = boxes[i]\n", "        \n", "        # 检查与所有其他框的重叠\n", "        for j in range(len(boxes)):\n", "            if j == i or j in suppressed:\n", "                continue\n", "                \n", "            iou = compute_iou(current_box, boxes[j])\n", "            if iou > iou_threshold:\n", "                suppressed.add(j)\n", "    \n", "    # 按原始顺序返回结果\n", "    keep.sort()\n", "    filtered_boxes = boxes[keep].tolist()\n", "    filtered_labels = labels[keep].tolist()\n", "    \n", "    return filtered_boxes, filtered_labels\n", "\n", "def filter_boxes_by_criteria(boxes, labels):\n", "    \"\"\"\n", "    根据尺寸和长宽比过滤框\n", "    \n", "    Args:\n", "        boxes: 框列表\n", "        labels: 标签列表\n", "    \n", "    Returns:\n", "        filtered_boxes: 过滤后的框\n", "        filtered_labels: 过滤后的标签\n", "    \"\"\"\n", "    filtered_boxes = []\n", "    filtered_labels = []\n", "    \n", "    for box, label in zip(boxes, labels):\n", "        xmin, ymin, xmax, ymax = box\n", "        w = xmax - xmin\n", "        h = ymax - ymin\n", "        area = w * h\n", "        ratio = w / h if h > 0 else 0\n", "        \n", "        # 过滤条件：\n", "        # 1. 长宽比 > 1.33或 <0.75的框\n", "        # 2. 面积<50的框\n", "        if ratio > 1.33 or ratio < 0.75:\n", "            continue\n", "            \n", "        filtered_boxes.append(box)\n", "        filtered_labels.append(label)\n", "    \n", "    return filtered_boxes, filtered_labels\n", "\n", "def remove_duplicate_boxes(boxes, labels, iou_threshold=0.2):\n", "    \"\"\"\n", "    移除重复的框（完全相同或几乎相同的框）\n", "    \n", "    Args:\n", "        boxes: 框列表\n", "        labels: 标签列表\n", "        iou_threshold: 重复判定阈值\n", "    \n", "    Returns:\n", "        filtered_boxes: 过滤后的框\n", "        filtered_labels: 过滤后的标签\n", "    \"\"\"\n", "    if len(boxes) == 0:\n", "        return [], []\n", "    \n", "    boxes = np.array(boxes)\n", "    labels = np.array(labels)\n", "    \n", "    # 计算所有框的面积\n", "    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])\n", "    \n", "    # 按面积从小到大排序\n", "    sorted_indices = np.argsort(areas)\n", "    \n", "    keep = []\n", "    suppressed = set()\n", "    \n", "    for i in sorted_indices:\n", "        if i in suppressed:\n", "            continue\n", "            \n", "        keep.append(i)\n", "        current_box = boxes[i]\n", "        \n", "        # 检查与后续框的重复\n", "        for j in sorted_indices[i+1:]:\n", "            if j in suppressed:\n", "                continue\n", "                \n", "            iou = compute_iou(current_box, boxes[j])\n", "            if iou > iou_threshold:\n", "                suppressed.add(j)\n", "    \n", "    # 按原始顺序返回结果\n", "    keep.sort()\n", "    filtered_boxes = boxes[keep].tolist()\n", "    filtered_labels = labels[keep].tolist()\n", "    \n", "    return filtered_boxes, filtered_labels\n", "\n", "# 重新遍历xml，收集所有框\n", "for xml_file in xml_files:\n", "    try:\n", "        tree = ET.parse(xml_file)\n", "        root = tree.getroot()\n", "        jpg_path = Path(str(xml_file).replace('.xml', '.jpg'))\n", "        if not jpg_path.exists():\n", "            continue\n", "        img = cv2.imread(str(jpg_path))\n", "        if img is None:\n", "            continue\n", "\n", "        boxes = []\n", "        labels = []\n", "        for obj in root.findall('object'):\n", "            name_elem = obj.find('name')\n", "            label = name_elem.text.strip() if name_elem is not None and name_elem.text else \"(none)\"\n", "            bndbox = obj.find('bndbox')\n", "            if bndbox is not None:\n", "                try:\n", "                    xmin = float(bndbox.find('xmin').text)\n", "                    ymin = float(bndbox.find('ymin').text)\n", "                    xmax = float(bndbox.find('xmax').text)\n", "                    ymax = float(bndbox.find('ymax').text)\n", "                    boxes.append([xmin, ymin, xmax, ymax])\n", "                    labels.append(label)\n", "                except Exception as e:\n", "                    continue\n", "\n", "        print(f\"原始框数量: {len(boxes)}\")\n", "        \n", "        # 第一步：移除重复的框\n", "        boxes, labels = remove_duplicate_boxes(boxes, labels, iou_threshold=0.2)\n", "        print(f\"移除重复后框数量: {len(boxes)}\")\n", "        \n", "        # 第二步：根据尺寸和长宽比过滤\n", "        filtered_boxes, filtered_labels = filter_boxes_by_criteria(boxes, labels)\n", "        print(f\"尺寸过滤后框数量: {len(filtered_boxes)}\")\n", "        \n", "        # 第三步：使用改进的NMS处理重叠框，保留最小的框\n", "        final_boxes, final_labels = nms_keep_smallest_improved(filtered_boxes, filtered_labels, iou_threshold=0.2)\n", "        print(f\"NMS后框数量: {len(final_boxes)}\")\n", "\n", "        # 可视化\n", "        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "        plt.figure(figsize=(8, 6))\n", "        plt.imshow(img_rgb)\n", "        ax = plt.gca()\n", "        \n", "        for box, label in zip(final_boxes, final_labels):\n", "            xmin, ymin, xmax, ymax = box\n", "            w = xmax - xmin\n", "            h = ymax - ymin\n", "            color = 'g' if \"green\" in label else 'r' if \"red\" in label else 'b'\n", "            rect = plt.Rectangle((xmin, ymin), w, h, fill=False, edgecolor=color, linewidth=2)\n", "            ax.add_patch(rect)\n", "            ax.text(xmin, ymin-3, label, fontsize=10, color=color, backgroundcolor='w')\n", "        \n", "        plt.title(f\"Filtered boxes: {jpg_path.stem}\")\n", "        plt.axis('off')\n", "        plt.show()\n", "\n", "        # # 只演示前3张\n", "        # if xml_files.index(xml_file) >= 2:\n", "        #     break\n", "\n", "    except Exception as e:\n", "        print(f\"Error parsing {xml_file.name}: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "b4b6592d", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import cv2\n", "from pathlib import Path\n", "import xml.etree.ElementTree as ET\n", "\n", "def compute_iou(box1, box2):\n", "    \"\"\"计算两个框的IoU\"\"\"\n", "    # box: (xmin, ymin, xmax, ymax)\n", "    xA = max(box1[0], box2[0])\n", "    yA = max(box1[1], box2[1])\n", "    xB = min(box1[2], box2[2])\n", "    yB = min(box1[3], box2[3])\n", "    interW = max(0, xB - xA)\n", "    interH = max(0, yB - yA)\n", "    interArea = interW * interH\n", "    area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])\n", "    area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])\n", "    unionArea = area1 + area2 - interArea\n", "    if unionArea == 0:\n", "        return 0\n", "    return interArea / unionArea\n", "\n", "def compute_area(box):\n", "    \"\"\"计算框的面积\"\"\"\n", "    return (box[2] - box[0]) * (box[3] - box[1])\n", "\n", "def nms_keep_smallest_improved(boxes, labels, iou_threshold=0.2):\n", "    \"\"\"\n", "    改进的NMS算法，在重叠情况下保留最小的框\n", "    \n", "    Args:\n", "        boxes: 框列表，格式为 [(xmin, ymin, xmax, ymax), ...]\n", "        labels: 标签列表\n", "        iou_threshold: IoU阈值（降低到0.2）\n", "    \n", "    Returns:\n", "        filtered_boxes: 过滤后的框\n", "        filtered_labels: 过滤后的标签\n", "    \"\"\"\n", "    if len(boxes) == 0:\n", "        return [], []\n", "    \n", "    # 转换为numpy数组以便处理\n", "    boxes = np.array(boxes)\n", "    labels = np.array(labels)\n", "    \n", "    # 计算所有框的面积\n", "    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])\n", "    \n", "    # 按面积从小到大排序（保留最小的框）\n", "    sorted_indices = np.argsort(areas)\n", "    \n", "    keep = []\n", "    suppressed = set()\n", "    \n", "    for i in sorted_indices:\n", "        if i in suppressed:\n", "            continue\n", "            \n", "        keep.append(i)\n", "        current_box = boxes[i]\n", "        \n", "        # 检查与所有其他框的重叠\n", "        for j in range(len(boxes)):\n", "            if j == i or j in suppressed:\n", "                continue\n", "                \n", "            iou = compute_iou(current_box, boxes[j])\n", "            if iou > iou_threshold:\n", "                suppressed.add(j)\n", "    \n", "    # 按原始顺序返回结果\n", "    keep.sort()\n", "    filtered_boxes = boxes[keep].tolist()\n", "    filtered_labels = labels[keep].tolist()\n", "    \n", "    return filtered_boxes, filtered_labels\n", "\n", "def filter_boxes_by_criteria(boxes, labels):\n", "    \"\"\"\n", "    根据尺寸和长宽比过滤框\n", "    \n", "    Args:\n", "        boxes: 框列表\n", "        labels: 标签列表\n", "    \n", "    Returns:\n", "        filtered_boxes: 过滤后的框\n", "        filtered_labels: 过滤后的标签\n", "    \"\"\"\n", "    filtered_boxes = []\n", "    filtered_labels = []\n", "    \n", "    for box, label in zip(boxes, labels):\n", "        xmin, ymin, xmax, ymax = box\n", "        w = xmax - xmin\n", "        h = ymax - ymin\n", "        area = w * h\n", "        ratio = w / h if h > 0 else 0\n", "        \n", "        # 过滤条件：\n", "        # 1. 长宽比 > 1.33或 <0.75的框\n", "        # 2. 面积<50的框\n", "        if ratio > 1.33 or ratio < 0.75:\n", "            continue\n", "            \n", "        filtered_boxes.append(box)\n", "        filtered_labels.append(label)\n", "    \n", "    return filtered_boxes, filtered_labels\n", "\n", "def remove_duplicate_boxes(boxes, labels, iou_threshold=0.2):\n", "    \"\"\"\n", "    移除重复的框（完全相同或几乎相同的框）\n", "    \n", "    Args:\n", "        boxes: 框列表\n", "        labels: 标签列表\n", "        iou_threshold: 重复判定阈值\n", "    \n", "    Returns:\n", "        filtered_boxes: 过滤后的框\n", "        filtered_labels: 过滤后的标签\n", "    \"\"\"\n", "    if len(boxes) == 0:\n", "        return [], []\n", "    \n", "    boxes = np.array(boxes)\n", "    labels = np.array(labels)\n", "    \n", "    # 计算所有框的面积\n", "    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])\n", "    \n", "    # 按面积从小到大排序\n", "    sorted_indices = np.argsort(areas)\n", "    \n", "    keep = []\n", "    suppressed = set()\n", "    \n", "    for i in sorted_indices:\n", "        if i in suppressed:\n", "            continue\n", "            \n", "        keep.append(i)\n", "        current_box = boxes[i]\n", "        \n", "        # 检查与后续框的重复\n", "        for j in sorted_indices[i+1:]:\n", "            if j in suppressed:\n", "                continue\n", "                \n", "            iou = compute_iou(current_box, boxes[j])\n", "            if iou > iou_threshold:\n", "                suppressed.add(j)\n", "    \n", "    # 按原始顺序返回结果\n", "    keep.sort()\n", "    filtered_boxes = boxes[keep].tolist()\n", "    filtered_labels = labels[keep].tolist()\n", "    \n", "    return filtered_boxes, filtered_labels\n", "\n", "def filter_boxes_by_area_statistics(boxes, labels, iqr_multiplier=1.5):\n", "    \"\"\"\n", "    基于面积统计值过滤框，使用IQR方法去除异常值\n", "    \n", "    Args:\n", "        boxes: 框列表\n", "        labels: 标签列表\n", "        iqr_multiplier: IQR倍数，默认为1.5\n", "    \n", "    Returns:\n", "        filtered_boxes: 过滤后的框\n", "        filtered_labels: 过滤后的标签\n", "        area_stats: 面积统计信息\n", "    \"\"\"\n", "    if len(boxes) == 0:\n", "        return [], [], {}\n", "    \n", "    # 计算所有框的面积\n", "    areas = [compute_area(box) for box in boxes]\n", "    areas = np.array(areas)\n", "    \n", "    # 计算统计值\n", "    median_area = np.median(areas)\n", "    q1 = np.percentile(areas, 25)\n", "    q3 = np.percentile(areas, 75)\n", "    iqr = q3 - q1\n", "    \n", "    # 计算上下界\n", "    lower_bound = median_area - iqr_multiplier * iqr\n", "    upper_bound = median_area + iqr_multiplier * iqr\n", "    \n", "    # 过滤框\n", "    filtered_boxes = []\n", "    filtered_labels = []\n", "    removed_indices = []\n", "    \n", "    for i, (box, label) in enumerate(zip(boxes, labels)):\n", "        area = areas[i]\n", "        if lower_bound <= area <= upper_bound:\n", "            filtered_boxes.append(box)\n", "            filtered_labels.append(label)\n", "        else:\n", "            removed_indices.append(i)\n", "    \n", "    # 统计信息\n", "    area_stats = {\n", "        'median': median_area,\n", "        'q1': q1,\n", "        'q3': q3,\n", "        'iqr': iqr,\n", "        'lower_bound': lower_bound,\n", "        'upper_bound': upper_bound,\n", "        'total_boxes': len(boxes),\n", "        'filtered_boxes': len(filtered_boxes),\n", "        'removed_boxes': len(removed_indices),\n", "        'removed_indices': removed_indices,\n", "        'areas': areas.tolist()\n", "    }\n", "    \n", "    return filtered_boxes, filtered_labels, area_stats\n", "\n", "# 重新遍历xml，收集所有框\n", "for xml_file in xml_files:\n", "    try:\n", "        tree = ET.parse(xml_file)\n", "        root = tree.getroot()\n", "        jpg_path = Path(str(xml_file).replace('.xml', '.jpg'))\n", "        if not jpg_path.exists():\n", "            continue\n", "        img = cv2.imread(str(jpg_path))\n", "        if img is None:\n", "            continue\n", "\n", "        boxes = []\n", "        labels = []\n", "        for obj in root.findall('object'):\n", "            name_elem = obj.find('name')\n", "            label = name_elem.text.strip() if name_elem is not None and name_elem.text else \"(none)\"\n", "            bndbox = obj.find('bndbox')\n", "            if bndbox is not None:\n", "                try:\n", "                    xmin = float(bndbox.find('xmin').text)\n", "                    ymin = float(bndbox.find('ymin').text)\n", "                    xmax = float(bndbox.find('xmax').text)\n", "                    ymax = float(bndbox.find('ymax').text)\n", "                    boxes.append([xmin, ymin, xmax, ymax])\n", "                    labels.append(label)\n", "                except Exception as e:\n", "                    continue\n", "\n", "        print(f\"原始框数量: {len(boxes)}\")\n", "        \n", "        # 第一步：移除重复的框\n", "        boxes, labels = remove_duplicate_boxes(boxes, labels, iou_threshold=0.2)\n", "        print(f\"移除重复后框数量: {len(boxes)}\")\n", "        \n", "        # 第二步：根据尺寸和长宽比过滤\n", "        filtered_boxes, filtered_labels = filter_boxes_by_criteria(boxes, labels)\n", "        print(f\"尺寸过滤后框数量: {len(filtered_boxes)}\")\n", "        \n", "        # 第三步：使用改进的NMS处理重叠框，保留最小的框\n", "        nms_boxes, nms_labels = nms_keep_smallest_improved(filtered_boxes, filtered_labels, iou_threshold=0.2)\n", "        print(f\"NMS后框数量: {len(nms_boxes)}\")\n", "        \n", "        # 第四步：基于面积统计值过滤异常值\n", "        final_boxes, final_labels, area_stats = filter_boxes_by_area_statistics(nms_boxes, nms_labels, iqr_multiplier=1.5)\n", "        print(f\"面积统计过滤后框数量: {len(final_boxes)}\")\n", "        \n", "        # 打印面积统计信息\n", "        print(f\"面积统计信息:\")\n", "        print(f\"  中位数: {area_stats['median']:.2f}\")\n", "        print(f\"  Q1: {area_stats['q1']:.2f}, Q3: {area_stats['q3']:.2f}\")\n", "        print(f\"  IQR: {area_stats['iqr']:.2f}\")\n", "        print(f\"  下界: {area_stats['lower_bound']:.2f}, 上界: {area_stats['upper_bound']:.2f}\")\n", "        print(f\"  移除的框数量: {area_stats['removed_boxes']}\")\n", "\n", "        # 可视化\n", "        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "        plt.figure(figsize=(12, 8))\n", "        \n", "        # 创建子图\n", "        plt.subplot(1, 2, 1)\n", "        plt.imshow(img_rgb)\n", "        ax = plt.gca()\n", "        \n", "        for box, label in zip(final_boxes, final_labels):\n", "            xmin, ymin, xmax, ymax = box\n", "            w = xmax - xmin\n", "            h = ymax - ymin\n", "            color = 'g' if \"green\" in label else 'r' if \"red\" in label else 'b'\n", "            rect = plt.Rectangle((xmin, ymin), w, h, fill=False, edgecolor=color, linewidth=2)\n", "            ax.add_patch(rect)\n", "            ax.text(xmin, ymin-3, label, fontsize=10, color=color, backgroundcolor='w')\n", "        \n", "        plt.title(f\"Filtered boxes: {jpg_path.stem}\")\n", "        plt.axis('off')\n", "        \n", "        # 添加面积分布图\n", "        plt.subplot(1, 2, 2)\n", "        if len(area_stats['areas']) > 0:\n", "            plt.hist(area_stats['areas'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')\n", "            plt.axvline(area_stats['median'], color='red', linestyle='--', label=f'Median: {area_stats[\"median\"]:.2f}')\n", "            plt.axvline(area_stats['lower_bound'], color='orange', linestyle='--', label=f'Lower bound: {area_stats[\"lower_bound\"]:.2f}')\n", "            plt.axvline(area_stats['upper_bound'], color='orange', linestyle='--', label=f'Upper bound: {area_stats[\"upper_bound\"]:.2f}')\n", "            plt.xlabel('Box Area')\n", "            plt.ylabel('Frequency')\n", "            plt.title('Area Distribution')\n", "            plt.legend()\n", "            plt.grid(True, alpha=0.3)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "        # # 只演示前3张\n", "        # if xml_files.index(xml_file) >= 2:\n", "        #     break\n", "\n", "    except Exception as e:\n", "        print(f\"Error parsing {xml_file.name}: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "yolo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}